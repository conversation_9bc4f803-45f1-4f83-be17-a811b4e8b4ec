# File Management System Implementation

## Overview
This document outlines the implementation of a comprehensive file management system for the SIT-BE application, supporting both temporary and permanent file repositories for each assignment with advanced file management features.

## Key Features Implemented

### 1. File Repository Structure
- **Temporary Files Repository**: Automatically aggregates all files uploaded via chat from all Assignment Groups belonging to that assignment
- **Permanent Files Repository**: Contains files that instructors can directly upload and manage
- Each GroupAssignment automatically gets both repository types created

### 2. Enhanced Document Entity
The Document entity now supports:
- **File Size**: Stored in bytes (bigint) with 10MB maximum constraint
- **File Type**: Enum supporting PDF, DOCX, PPTX, XLSX, CSV, PNG, JPG, JPEG, TXT
- **Visibility Status**: Toggle between "Hidden" and "Visible" for instructor control
- **Tags**: Array of strings for categorization and filtering
- **Description**: Up to 120 characters for file descriptions
- **Thread Association**: Links chat-uploaded files to specific MongoDB thread IDs
- **Repository Classification**: Associates files with temporary or permanent repositories

### 3. Database Schema Changes

#### New FileRepository Entity
```typescript
@Entity('file_repositories')
export class FileRepository extends BaseEntity {
  name: string;                    // Repository name
  description: string;             // Repository description
  type: FileRepositoryType;        // 'temporary' | 'permanent'
  isActive: boolean;               // Active status
  groupAssignmentId: string;       // Foreign key to GroupAssignment
  groupAssignment: GroupAssignment; // Relationship
  documents: Document[];           // One-to-many with documents
}
```

#### Enhanced Document Entity
```typescript
@Entity('documents')
export class Document extends BaseEntity {
  fileUrl: string;                 // File URL (existing)
  fileName: string;                // File name (existing)
  fileSize: number;                // File size in bytes (NEW)
  fileType: DocumentFileType;      // File type enum (NEW)
  visibilityStatus: DocumentVisibilityStatus; // Visibility control (NEW)
  description: string;             // File description (NEW)
  tags: string[];                  // Tags array (NEW)
  threadId: string;                // MongoDB thread ID (NEW)
  uploaderId: string;              // Foreign key to User (existing)
  groupId: string;                 // Foreign key to GroupAssignment (existing)
  fileRepositoryId: string;        // Foreign key to FileRepository (NEW)
  // Relationships...
}
```

## File Upload and Association Logic

### Chat-Uploaded Files
1. Files uploaded in chat threads are automatically associated with:
   - The specific thread (threadId field)
   - The assignment's "Temporary" repository
   - Default visibility status: "Visible"
   - Auto-generated tags from thread/conversation names

### Instructor-Uploaded Files
1. Files directly uploaded by instructors are associated with:
   - The assignment's "Permanent" repository
   - No thread association (threadId = null)
   - Instructor-controlled visibility status
   - Manual tag assignment

## File Constraints
- **Maximum Size**: 10MB (10,485,760 bytes)
- **Allowed Types**: PDF, DOCX, PPTX, XLSX, CSV, PNG, JPG, JPEG, TXT
- **Description Limit**: 120 characters
- **Tags**: Unlimited array of strings

## Instructor File Management Features

### 1. Visibility Control
- Toggle files between "Hidden" and "Visible" status
- Hidden files are not visible to students
- Visible files appear in student file lists

### 2. Tagging System
- Add/remove tags for file categorization
- Auto-suggest tags from existing thread/conversation names
- Support for multiple tags per file

### 3. File Descriptions
- Add/edit descriptions up to 120 characters
- Helps provide context for files

## File List Display Columns
The instructor's file list view includes:
- **Name**: File name with download link
- **Uploaded by**: User who uploaded the file
- **Last modified**: File's updatedAt timestamp
- **File size**: Human-readable file size
- **Hidden/Visible status**: Current visibility status with toggle
- **Tag**: Comma-separated list of tags
- **Description**: File description
- **Download action**: Direct download button/link

## Database Migration Files

### 1. CreateFileRepositoriesTable (1739438560000)
Creates the file_repositories table with:
- Basic entity fields (id, created_at, updated_at, deleted_at)
- Repository-specific fields (name, description, type, is_active)
- Foreign key to group_assignments

### 2. Updated CreateDocumentsTable (1739438570000)
Enhanced the documents table with:
- file_size (bigint)
- file_type (enum)
- visibility_status (enum, default 'visible')
- description (varchar 120)
- tags (text, stored as comma-separated)
- thread_id (varchar 255, nullable)
- file_repository_id (uuid, nullable, foreign key)

## Seed Data
The seed files demonstrate the system with:
- 6 file repositories (2 per assignment: temporary + permanent)
- 6 sample documents showing various scenarios:
  - Instructor permanent files (visible and hidden)
  - Student temporary files from chat
  - Different file types and sizes
  - Various tags and descriptions

## Usage Examples

### Creating File Repositories
File repositories are automatically created when seeding:
```typescript
// Temporary repository
{
  name: "Final Project - E-commerce Platform - Temporary Files",
  type: FileRepositoryType.TEMPORARY,
  groupAssignmentId: assignment.id
}

// Permanent repository  
{
  name: "Final Project - E-commerce Platform - Permanent Files",
  type: FileRepositoryType.PERMANENT,
  groupAssignmentId: assignment.id
}
```

### Document Examples
```typescript
// Chat-uploaded file (temporary)
{
  fileName: "Database Schema Design.pdf",
  fileSize: 1536000,
  fileType: DocumentFileType.PDF,
  visibilityStatus: DocumentVisibilityStatus.VISIBLE,
  tags: ['database', 'schema', 'draft'],
  threadId: '507f1f77bcf86cd799439011',
  fileRepositoryId: tempRepository.id
}

// Instructor file (permanent, hidden)
{
  fileName: "Grading Rubric.pdf",
  fileSize: 512000,
  fileType: DocumentFileType.PDF,
  visibilityStatus: DocumentVisibilityStatus.HIDDEN,
  tags: ['grading', 'internal', 'rubric'],
  threadId: null,
  fileRepositoryId: permRepository.id
}
```

## Next Steps
1. Implement file upload API endpoints with validation
2. Create instructor dashboard for file management
3. Add file search and filtering capabilities
4. Implement automatic tag generation from thread names
5. Add file versioning support
6. Create student file access controls
