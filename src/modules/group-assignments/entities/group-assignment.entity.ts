import { <PERSON><PERSON><PERSON>, Column, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';
import { Course } from '@modules/courses/entities/course.entity';
import { GroupUser } from '@modules/group-users/entities/group-user.entity';
import { GroupAssignmentCourse } from '@modules/group-assignment-courses/entities/group-assignment-course.entity';
import { Document } from '@modules/documents/entities/document.entity';
import { FileRepository } from '@modules/file-repositories/entities/file-repository.entity';

@Entity('group_assignments')
export class GroupAssignment extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  createdBy: string;

  @Column({ type: 'uuid' })
  courseId: string;

  // Entity relationships
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => Course, 'groupAssignments', {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  course: Course;

  @OneToMany(() => GroupUser, 'group')
  groupUsers: GroupUser[];

  @OneToMany(() => Document, 'group')
  documents: Document[];

  @OneToMany(() => GroupAssignmentCourse, 'group')
  groupAssignmentCourses: GroupAssignmentCourse[];

  @OneToMany(() => FileRepository, 'groupAssignment')
  fileRepositories: FileRepository[];
}
