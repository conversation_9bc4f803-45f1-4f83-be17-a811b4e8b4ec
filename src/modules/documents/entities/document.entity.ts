import { Entity, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@modules/group-assignments/entities/group-assignment.entity';
import { FileRepository } from '@modules/file-repositories/entities/file-repository.entity';

export enum DocumentVisibilityStatus {
  VISIBLE = 'visible',
  HIDDEN = 'hidden',
}

export enum DocumentFileType {
  PDF = 'pdf',
  DOCX = 'docx',
  PPTX = 'pptx',
  XLSX = 'xlsx',
  CSV = 'csv',
  PNG = 'png',
  JPG = 'jpg',
  JPEG = 'jpeg',
  TXT = 'txt',
}

@Entity('documents')
export class Document extends BaseEntity {
  @Column({ type: 'varchar', length: 500 })
  fileUrl: string;

  @Column({ type: 'varchar', length: 255 })
  fileName: string;

  @Column({ type: 'integer' })
  fileSize: number;

  @Column({
    type: 'enum',
    enum: DocumentFileType,
  })
  fileType: DocumentFileType;

  @Column({
    type: 'enum',
    enum: DocumentVisibilityStatus,
    default: DocumentVisibilityStatus.VISIBLE,
  })
  visibilityStatus: DocumentVisibilityStatus;

  @Column({ type: 'varchar', length: 120, nullable: true })
  description: string;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ type: 'varchar', length: 255, nullable: true })
  threadId: string; // MongoDB thread ID for chat-uploaded files

  // Foreign key relationships
  @Column({ type: 'uuid' })
  uploaderId: string;

  @Column({ type: 'uuid' })
  groupId: string;

  @Column({ type: 'uuid', nullable: true })
  fileRepositoryId: string;

  // Entity relationships
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'uploader_id' })
  uploader: User;

  @ManyToOne(() => GroupAssignment, 'documents', { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'group_id' })
  group: GroupAssignment;

  @ManyToOne(() => FileRepository, 'documents', { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'file_repository_id' })
  fileRepository: FileRepository;
}
