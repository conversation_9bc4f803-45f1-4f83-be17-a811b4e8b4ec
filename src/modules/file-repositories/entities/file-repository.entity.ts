import { <PERSON>ti<PERSON>, Column, ManyToOne, OneToMany, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { GroupAssignment } from '@modules/group-assignments/entities/group-assignment.entity';
import { Document } from '@modules/documents/entities/document.entity';

export enum FileRepositoryType {
  TEMPORARY = 'temporary',
  PERMANENT = 'permanent',
}

@Entity('file_repositories')
export class FileRepository extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: FileRepositoryType,
    default: FileRepositoryType.TEMPORARY,
  })
  type: FileRepositoryType;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  groupAssignmentId: string;

  // Entity relationships
  @ManyToOne(() => GroupAssignment, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'group_assignment_id' })
  groupAssignment: GroupAssignment;

  @OneToMany(() => Document, 'fileRepository')
  documents: Document[];
}
