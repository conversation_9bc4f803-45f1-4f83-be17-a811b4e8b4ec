import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@modules/group-assignments/entities/group-assignment.entity';
import {
  Document,
  DocumentFileType,
  DocumentVisibilityStatus,
} from '@modules/documents/entities/document.entity';
import {
  FileRepository,
  FileRepositoryType,
} from '@modules/file-repositories/entities/file-repository.entity';

export async function seedDocuments(
  dataSource: DataSource,
  users: User[],
  groupAssignments: GroupAssignment[],
  fileRepositories: FileRepository[],
): Promise<Document[]> {
  console.log('🌱 Seeding documents...');
  const documentRepository = dataSource.getRepository(Document);

  // Check if documents already exist
  const existingDocuments = await documentRepository.find();
  if (existingDocuments.length > 0) {
    console.log('✅ Documents already exist, skipping document seeding');
    return existingDocuments;
  }

  const instructor = users.find((u) => u.name === 'Dr. <PERSON>')!;
  const student1 = users.find((u) => u.name === 'Alex')!;
  const student2 = users.find((u) => u.name === 'Watson')!;

  // Get repositories for each assignment
  const tempRepo1 = fileRepositories.find(
    (r) =>
      r.groupAssignmentId === groupAssignments[0].id &&
      r.type === FileRepositoryType.TEMPORARY,
  )!;
  const permRepo1 = fileRepositories.find(
    (r) =>
      r.groupAssignmentId === groupAssignments[0].id &&
      r.type === FileRepositoryType.PERMANENT,
  )!;
  const tempRepo2 = fileRepositories.find(
    (r) =>
      r.groupAssignmentId === groupAssignments[1].id &&
      r.type === FileRepositoryType.TEMPORARY,
  )!;
  const permRepo2 = fileRepositories.find(
    (r) =>
      r.groupAssignmentId === groupAssignments[1].id &&
      r.type === FileRepositoryType.PERMANENT,
  )!;
  const tempRepo3 = fileRepositories.find(
    (r) =>
      r.groupAssignmentId === groupAssignments[2].id &&
      r.type === FileRepositoryType.TEMPORARY,
  )!;
  const permRepo3 = fileRepositories.find(
    (r) =>
      r.groupAssignmentId === groupAssignments[2].id &&
      r.type === FileRepositoryType.PERMANENT,
  )!;

  const documents = [
    // Instructor-uploaded permanent files
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/ecommerce-requirements.pdf',
      fileName: 'Project Requirements Document.pdf',
      fileSize: 2048576, // 2MB
      fileType: DocumentFileType.PDF,
      visibilityStatus: DocumentVisibilityStatus.VISIBLE,
      description: 'Official project requirements and specifications',
      tags: ['requirements', 'official', 'project-specs'],
      threadId: null, // Not from chat
      uploaderId: instructor.id,
      groupId: groupAssignments[0].id,
      fileRepositoryId: permRepo1.id,
    },
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/grading-rubric.pdf',
      fileName: 'Grading Rubric.pdf',
      fileSize: 512000, // 500KB
      fileType: DocumentFileType.PDF,
      visibilityStatus: DocumentVisibilityStatus.HIDDEN,
      description: 'Internal grading criteria - not visible to students',
      tags: ['grading', 'internal', 'rubric'],
      threadId: null,
      uploaderId: instructor.id,
      groupId: groupAssignments[0].id,
      fileRepositoryId: permRepo1.id,
    },
    // Student-uploaded temporary files (from chat)
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/library-schema.pdf',
      fileName: 'Database Schema Design.pdf',
      fileSize: 1536000, // 1.5MB
      fileType: DocumentFileType.PDF,
      visibilityStatus: DocumentVisibilityStatus.VISIBLE,
      description: 'Initial database schema proposal',
      tags: ['database', 'schema', 'draft'],
      threadId: '507f1f77bcf86cd799439011', // MongoDB ObjectId format
      uploaderId: student1.id,
      groupId: groupAssignments[1].id,
      fileRepositoryId: tempRepo2.id,
    },
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/ml-training-data.csv',
      fileName: 'ML Model Training Data.csv',
      fileSize: 5242880, // 5MB
      fileType: DocumentFileType.CSV,
      visibilityStatus: DocumentVisibilityStatus.VISIBLE,
      description: 'Training dataset for predictive model',
      tags: ['machine-learning', 'dataset', 'training'],
      threadId: '507f1f77bcf86cd799439012',
      uploaderId: student2.id,
      groupId: groupAssignments[2].id,
      fileRepositoryId: tempRepo3.id,
    },
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/project-presentation.pptx',
      fileName: 'Final Presentation.pptx',
      fileSize: 8388608, // 8MB
      fileType: DocumentFileType.PPTX,
      visibilityStatus: DocumentVisibilityStatus.VISIBLE,
      description: 'Final project presentation slides',
      tags: ['presentation', 'final', 'slides'],
      threadId: '507f1f77bcf86cd799439013',
      uploaderId: student1.id,
      groupId: groupAssignments[0].id,
      fileRepositoryId: tempRepo1.id,
    },
    // Additional instructor permanent file
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/reference-materials.docx',
      fileName: 'Reference Materials.docx',
      fileSize: 1024000, // 1MB
      fileType: DocumentFileType.DOCX,
      visibilityStatus: DocumentVisibilityStatus.VISIBLE,
      description: 'Additional reading materials and references',
      tags: ['reference', 'reading', 'materials'],
      threadId: null,
      uploaderId: instructor.id,
      groupId: groupAssignments[2].id,
      fileRepositoryId: permRepo3.id,
    },
  ];

  const savedDocuments = await documentRepository.save(documents);
  console.log(`✅ Created ${savedDocuments.length} documents`);
  return savedDocuments;
}
