import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@modules/group-assignments/entities/group-assignment.entity';
import {
  Document,
  DocumentStatus,
  DocumentRepositoryType,
} from '@modules/documents/entities/document.entity';

export async function seedDocuments(
  dataSource: DataSource,
  users: User[],
  groupAssignments: GroupAssignment[],
): Promise<Document[]> {
  console.log('🌱 Seeding documents...');
  const documentRepository = dataSource.getRepository(Document);

  // Check if documents already exist
  const existingDocuments = await documentRepository.find();
  if (existingDocuments.length > 0) {
    console.log('✅ Documents already exist, skipping document seeding');
    return existingDocuments;
  }

  const instructor = users.find((u) => u.name === 'Dr. <PERSON>')!;
  const student1 = users.find((u) => u.name === 'Alex')!;
  const student2 = users.find((u) => u.name === 'Watson')!;

  const documents = [
    // Instructor-uploaded permanent files
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'Project Requirements Document.pdf',
      status: DocumentStatus.VISIBLE,
      description: 'Official project requirements and specifications',
      tags: ['requirements', 'official', 'project-specs'],
      threadId: undefined, // Not from chat
      repositoryType: DocumentRepositoryType.PERMANENT,
      uploaderId: instructor.id,
      groupId: groupAssignments[0].id,
    },
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'Grading Rubric.pdf',
      status: DocumentStatus.HIDDEN,
      description: 'Internal grading criteria - not visible to students',
      tags: ['grading', 'internal', 'rubric'],
      threadId: undefined,
      repositoryType: DocumentRepositoryType.PERMANENT,
      uploaderId: instructor.id,
      groupId: groupAssignments[0].id,
    },
    // Student-uploaded temporary files (from chat)
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'Database Schema Design.pdf',
      status: DocumentStatus.VISIBLE,
      description: 'Initial database schema proposal',
      tags: ['database', 'schema', 'draft'],
      threadId: '507f1f77bcf86cd799439011', // MongoDB ObjectId format
      repositoryType: DocumentRepositoryType.TEMPORARY,
      uploaderId: student1.id,
      groupId: groupAssignments[1].id,
    },
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'ML Model Training Data.csv',
      status: DocumentStatus.VISIBLE,
      description: 'Training dataset for predictive model',
      tags: ['machine-learning', 'dataset', 'training'],
      threadId: '507f1f77bcf86cd799439012',
      repositoryType: DocumentRepositoryType.TEMPORARY,
      uploaderId: student2.id,
      groupId: groupAssignments[2].id,
    },
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'Final Presentation.pptx',
      status: DocumentStatus.VISIBLE,
      description: 'Final project presentation slides',
      tags: ['presentation', 'final', 'slides'],
      threadId: '507f1f77bcf86cd799439013',
      repositoryType: DocumentRepositoryType.TEMPORARY,
      uploaderId: student1.id,
      groupId: groupAssignments[0].id,
    },
    // Additional instructor permanent file
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'Reference Materials.docx',
      status: DocumentStatus.VISIBLE,
      description: 'Additional reading materials and references',
      tags: ['reference', 'reading', 'materials'],
      threadId: undefined,
      repositoryType: DocumentRepositoryType.PERMANENT,
      uploaderId: instructor.id,
      groupId: groupAssignments[2].id,
    },
  ];

  const savedDocuments = await documentRepository.save(documents);
  console.log(`✅ Created ${savedDocuments.length} documents`);
  return savedDocuments;
}
