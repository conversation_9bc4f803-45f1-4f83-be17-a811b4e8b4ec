import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@modules/group-assignments/entities/group-assignment.entity';
import {
  Document,
  DocumentFileType,
  DocumentVisibilityStatus,
  DocumentRepositoryType,
} from '@modules/documents/entities/document.entity';

export async function seedDocuments(
  dataSource: DataSource,
  users: User[],
  groupAssignments: GroupAssignment[],
): Promise<Document[]> {
  console.log('🌱 Seeding documents...');
  const documentRepository = dataSource.getRepository(Document);

  // Check if documents already exist
  const existingDocuments = await documentRepository.find();
  if (existingDocuments.length > 0) {
    console.log('✅ Documents already exist, skipping document seeding');
    return existingDocuments;
  }

  const instructor = users.find((u) => u.name === 'Dr. <PERSON>')!;
  const student1 = users.find((u) => u.name === 'Alex')!;
  const student2 = users.find((u) => u.name === 'Watson')!;

  const documents = [
    // Instructor-uploaded permanent files
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/ecommerce-requirements.pdf',
      fileName: 'Project Requirements Document.pdf',
      fileSize: 2048576, // 2MB
      fileType: DocumentFileType.PDF,
      visibilityStatus: DocumentVisibilityStatus.VISIBLE,
      description: 'Official project requirements and specifications',
      tags: ['requirements', 'official', 'project-specs'],
      threadId: undefined, // Not from chat
      repositoryType: DocumentRepositoryType.PERMANENT,
      uploaderId: instructor.id,
      groupId: groupAssignments[0].id,
    },
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/grading-rubric.pdf',
      fileName: 'Grading Rubric.pdf',
      fileSize: 512000, // 500KB
      fileType: DocumentFileType.PDF,
      visibilityStatus: DocumentVisibilityStatus.HIDDEN,
      description: 'Internal grading criteria - not visible to students',
      tags: ['grading', 'internal', 'rubric'],
      threadId: undefined,
      repositoryType: DocumentRepositoryType.PERMANENT,
      uploaderId: instructor.id,
      groupId: groupAssignments[0].id,
    },
    // Student-uploaded temporary files (from chat)
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/library-schema.pdf',
      fileName: 'Database Schema Design.pdf',
      fileSize: 1536000, // 1.5MB
      fileType: DocumentFileType.PDF,
      visibilityStatus: DocumentVisibilityStatus.VISIBLE,
      description: 'Initial database schema proposal',
      tags: ['database', 'schema', 'draft'],
      threadId: '507f1f77bcf86cd799439011', // MongoDB ObjectId format
      repositoryType: DocumentRepositoryType.TEMPORARY,
      uploaderId: student1.id,
      groupId: groupAssignments[1].id,
    },
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/ml-training-data.csv',
      fileName: 'ML Model Training Data.csv',
      fileSize: 5242880, // 5MB
      fileType: DocumentFileType.CSV,
      visibilityStatus: DocumentVisibilityStatus.VISIBLE,
      description: 'Training dataset for predictive model',
      tags: ['machine-learning', 'dataset', 'training'],
      threadId: '507f1f77bcf86cd799439012',
      repositoryType: DocumentRepositoryType.TEMPORARY,
      uploaderId: student2.id,
      groupId: groupAssignments[2].id,
    },
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/project-presentation.pptx',
      fileName: 'Final Presentation.pptx',
      fileSize: 8388608, // 8MB
      fileType: DocumentFileType.PPTX,
      visibilityStatus: DocumentVisibilityStatus.VISIBLE,
      description: 'Final project presentation slides',
      tags: ['presentation', 'final', 'slides'],
      threadId: '507f1f77bcf86cd799439013',
      repositoryType: DocumentRepositoryType.TEMPORARY,
      uploaderId: student1.id,
      groupId: groupAssignments[0].id,
    },
    // Additional instructor permanent file
    {
      id: uuidv4(),
      fileUrl: 'https://example.com/documents/reference-materials.docx',
      fileName: 'Reference Materials.docx',
      fileSize: 1024000, // 1MB
      fileType: DocumentFileType.DOCX,
      visibilityStatus: DocumentVisibilityStatus.VISIBLE,
      description: 'Additional reading materials and references',
      tags: ['reference', 'reading', 'materials'],
      threadId: undefined,
      repositoryType: DocumentRepositoryType.PERMANENT,
      uploaderId: instructor.id,
      groupId: groupAssignments[2].id,
    },
  ];

  const savedDocuments = await documentRepository.save(documents);
  console.log(`✅ Created ${savedDocuments.length} documents`);
  return savedDocuments;
}
