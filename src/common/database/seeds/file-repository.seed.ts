import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { GroupAssignment } from '@modules/group-assignments/entities/group-assignment.entity';
import { FileRepository, FileRepositoryType } from '@modules/file-repositories/entities/file-repository.entity';

export async function seedFileRepositories(
  dataSource: DataSource,
  groupAssignments: GroupAssignment[],
): Promise<FileRepository[]> {
  console.log('🌱 Seeding file repositories...');
  const fileRepositoryRepository = dataSource.getRepository(FileRepository);

  // Check if file repositories already exist
  const existingRepositories = await fileRepositoryRepository.find();
  if (existingRepositories.length > 0) {
    console.log('✅ File repositories already exist, skipping file repository seeding');
    return existingRepositories;
  }

  const fileRepositories = [];

  // Create two repositories for each group assignment (temporary and permanent)
  for (const groupAssignment of groupAssignments) {
    // Temporary repository for chat-uploaded files
    fileRepositories.push({
      id: uuidv4(),
      name: `${groupAssignment.name} - Temporary Files`,
      description: 'Automatically aggregates all files uploaded via chat from all Assignment Groups belonging to this assignment',
      type: FileRepositoryType.TEMPORARY,
      isActive: true,
      groupAssignmentId: groupAssignment.id,
    });

    // Permanent repository for instructor-uploaded files
    fileRepositories.push({
      id: uuidv4(),
      name: `${groupAssignment.name} - Permanent Files`,
      description: 'Contains files that instructors can directly upload and manage',
      type: FileRepositoryType.PERMANENT,
      isActive: true,
      groupAssignmentId: groupAssignment.id,
    });
  }

  const savedRepositories = await fileRepositoryRepository.save(fileRepositories);
  console.log(`✅ Created ${savedRepositories.length} file repositories`);
  return savedRepositories;
}
