# File Management System - Refactored Implementation

## Overview
Đã refactor file management system để đơn giản hóa architecture bằng cách loại bỏ FileRepository entity và sử dụng enum `repositoryType` trự<PERSON> tiếp trong Document entity.

## Key Changes Made

### ✅ Removed FileRepository Entity
- Xóa `src/modules/file-repositories/entities/file-repository.entity.ts`
- Xóa migration `1739438560000-CreateFileRepositoriesTable.ts`
- Xóa seed file `file-repository.seed.ts`

### ✅ Simplified Document Entity
```typescript
@Entity('documents')
export class Document extends BaseEntity {
  fileUrl: string;
  fileName: string;
  fileSize: number;                // integer type (not bigint)
  fileType: DocumentFileType;
  visibilityStatus: DocumentVisibilityStatus;
  description: string;             // 120 chars max
  tags: string[];                  // Array of tags
  threadId: string;                // MongoDB thread ID (nullable)
  repositoryType: DocumentRepositoryType; // NEW: 'temporary' | 'permanent'
  uploaderId: string;
  groupId: string;
  // Relationships with User and GroupAssignment
}

export enum DocumentRepositoryType {
  TEMPORARY = 'temporary',
  PERMANENT = 'permanent',
}
```

### ✅ Updated Database Migration
- Thay `file_repository_id` bằng `repository_type` enum column
- Xóa foreign key constraint đến file_repositories table
- Sử dụng `integer` type cho file_size (thay vì bigint)

### ✅ Updated Seed Data
- Loại bỏ file repository seeding
- Cập nhật document seeds để sử dụng `repositoryType`
- Sửa `threadId: null` thành `threadId: undefined` để tránh TypeScript errors

### ✅ Updated Related Files
- Xóa FileRepository import từ GroupAssignment entity
- Cập nhật `run-seed.ts` để không gọi file repository seeding
- Xóa FileRepository relationship từ GroupAssignment

## Benefits of Refactoring

### 1. **Simplified Architecture**
- Ít entities hơn để maintain
- Ít relationships phức tạp
- Ít migrations cần thiết

### 2. **Better Performance**
- Không cần JOIN với file_repositories table
- Truy vấn đơn giản hơn
- Ít foreign key constraints

### 3. **Easier to Understand**
- Logic rõ ràng hơn: file có type temporary hoặc permanent
- Không cần tạo và quản lý repository entities
- Code dễ đọc và maintain hơn

## File Classification Logic

### Temporary Files (repositoryType = 'temporary')
- Files uploaded via chat by students
- Có `threadId` để link với MongoDB thread
- Tự động visible cho students
- Auto-tagged từ thread/conversation names

### Permanent Files (repositoryType = 'permanent')
- Files uploaded directly by instructors
- Không có `threadId` (undefined)
- Instructor control visibility (hidden/visible)
- Manual tagging

## Database Schema

### Documents Table Columns
```sql
CREATE TABLE documents (
  id UUID PRIMARY KEY,
  file_url VARCHAR NOT NULL,
  file_name VARCHAR NOT NULL,
  file_size INTEGER NOT NULL,           -- Changed from bigint
  file_type document_file_type_enum NOT NULL,
  visibility_status document_visibility_status_enum DEFAULT 'visible',
  description VARCHAR(120),
  tags TEXT,                            -- Comma-separated
  thread_id VARCHAR(255),               -- Nullable
  repository_type document_repository_type_enum DEFAULT 'temporary', -- NEW
  uploader_id UUID NOT NULL,
  group_id UUID NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  deleted_at TIMESTAMP,
  
  FOREIGN KEY (uploader_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (group_id) REFERENCES group_assignments(id) ON DELETE CASCADE
);
```

## Usage Examples

### Query Temporary Files
```typescript
const tempFiles = await documentRepository.find({
  where: { 
    groupId: assignmentId,
    repositoryType: DocumentRepositoryType.TEMPORARY 
  }
});
```

### Query Permanent Files
```typescript
const permFiles = await documentRepository.find({
  where: { 
    groupId: assignmentId,
    repositoryType: DocumentRepositoryType.PERMANENT,
    visibilityStatus: DocumentVisibilityStatus.VISIBLE
  }
});
```

### Create Chat-Uploaded File
```typescript
const chatFile = documentRepository.create({
  fileName: 'student-work.pdf',
  fileUrl: 'https://...',
  fileSize: 1024000,
  fileType: DocumentFileType.PDF,
  repositoryType: DocumentRepositoryType.TEMPORARY,
  threadId: '507f1f77bcf86cd799439011',
  uploaderId: studentId,
  groupId: assignmentId,
  tags: ['homework', 'draft'],
  description: 'Initial homework submission'
});
```

### Create Instructor File
```typescript
const instructorFile = documentRepository.create({
  fileName: 'requirements.pdf',
  fileUrl: 'https://...',
  fileSize: 2048000,
  fileType: DocumentFileType.PDF,
  repositoryType: DocumentRepositoryType.PERMANENT,
  threadId: undefined, // No thread association
  uploaderId: instructorId,
  groupId: assignmentId,
  visibilityStatus: DocumentVisibilityStatus.VISIBLE,
  tags: ['requirements', 'official'],
  description: 'Project requirements document'
});
```

## File Constraints (Unchanged)
- **Maximum Size**: 10MB (10,485,760 bytes)
- **Allowed Types**: PDF, DOCX, PPTX, XLSX, CSV, PNG, JPG, JPEG, TXT
- **Description Limit**: 120 characters
- **Tags**: Unlimited array of strings

## Next Steps
1. Test migrations với database mới
2. Implement service layer với simplified logic
3. Create API endpoints cho file management
4. Update frontend để sử dụng repositoryType thay vì repository entities
5. Add validation cho file constraints
6. Implement auto-tagging từ thread names
